package com.bxm.customer.service.strategy.impl;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.StatusValidationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 已交付待确认状态验证策略
 * 
 * 处理从"已交付待确认"状态到其他状态的转换验证
 * 
 * 支持的状态转换：
 * 1. PENDING_CONFIRMATION -> CONFIRMED_PENDING_DEDUCTION (会计确认正常)
 * 2. PENDING_CONFIRMATION -> SUBMITTED_PENDING_DELIVERY (退回重新交付)
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class PendingConfirmationValidationStrategy implements StatusValidationStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            return false;
        }
        
        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY;
    }

    @Override
    public void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        log.info("Validating status change from PENDING_CONFIRMATION to {} for order: {}", 
                request.getTargetStatus(), request.getDeliveryOrderNo());

        ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());
        
        if (targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            // 验证会计确认
            validateAccountingConfirmation(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            // 验证退回重新交付
            validateReturnToDelivery(order, request);
        } else {
            throw new IllegalArgumentException("不支持从已交付待确认状态转换到: " + request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION;
    }

    /**
     * 验证会计确认
     */
    private void validateAccountingConfirmation(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限（会计角色验证）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        
        // 验证账务信息是否完整
        if (order.getAccountingInfo() == null) {
            throw new IllegalArgumentException("账务类型信息不能为空");
        }
        
        // 验证账期信息
        if (order.getAccountingPeriodStart() == null || order.getAccountingPeriodEnd() == null) {
            throw new IllegalArgumentException("账期开始和结束时间不能为空");
        }
        
        // 验证账期逻辑
        if (order.getAccountingPeriodStart() > order.getAccountingPeriodEnd()) {
            throw new IllegalArgumentException("账期开始时间不能晚于结束时间");
        }
        
        // 验证联络人信息（用于后续扣款通知）
        if (order.getContactMobile() == null || order.getContactMobile().trim().isEmpty()) {
            throw new IllegalArgumentException("联络人手机号不能为空");
        }
        
        log.info("Accounting confirmation validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证退回重新交付
     */
    private void validateReturnToDelivery(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限（会计角色验证）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        
        // 验证退回原因
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("退回重新交付必须提供原因");
        }
        
        // 退回原因长度验证
        if (request.getReason().length() < 5) {
            throw new IllegalArgumentException("退回原因描述不能少于5个字符");
        }
        
        log.info("Return to delivery validation passed for order: {}", request.getDeliveryOrderNo());
    }
}
