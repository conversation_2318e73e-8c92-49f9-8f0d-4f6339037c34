package com.bxm.customer.service.strategy.impl;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.StatusValidationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 交付异常状态验证策略
 * 
 * 处理从"交付异常(待确认)"状态到其他状态的转换验证
 * 
 * 支持的状态转换：
 * 1. DELIVERY_EXCEPTION -> DELIVERY_CLOSED (关闭交付)
 * 2. DELIVERY_EXCEPTION -> SUBMITTED_PENDING_DELIVERY (重新交付)
 * 3. DELIVERY_EXCEPTION -> PENDING_CONFIRMATION (异常处理完成，直接进入待确认)
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class DeliveryExceptionValidationStrategy implements StatusValidationStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION) {
            return false;
        }
        
        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED ||
               targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY ||
               targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION;
    }

    @Override
    public void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        log.info("Validating status change from DELIVERY_EXCEPTION to {} for order: {}", 
                request.getTargetStatus(), request.getDeliveryOrderNo());

        ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());
        
        if (targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED) {
            // 验证关闭交付
            validateCloseDelivery(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            // 验证重新交付
            validateRetryDelivery(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            // 验证异常处理完成
            validateExceptionResolved(order, request);
        } else {
            throw new IllegalArgumentException("不支持从交付异常状态转换到: " + request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION;
    }

    /**
     * 验证关闭交付
     * 
     * 适用场景：
     * 1. 客户取消交付需求
     * 2. 交付条件无法满足
     * 3. 业务变更，不再需要交付
     */
    private void validateCloseDelivery(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限（需要业务主管或更高权限）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        
        // 验证关闭原因
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("关闭交付必须提供详细原因");
        }
        
        // 关闭原因长度验证
        if (request.getReason().length() < 15) {
            throw new IllegalArgumentException("关闭交付原因描述不能少于15个字符");
        }
        
        // 验证备注信息（用于记录关闭决策过程）
        if (request.getRemark() == null || request.getRemark().trim().isEmpty()) {
            throw new IllegalArgumentException("关闭交付必须提供备注信息");
        }
        
        log.info("Close delivery validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证重新交付
     * 
     * 适用场景：
     * 1. 异常问题已解决，可以重新交付
     * 2. 交付条件已满足
     * 3. 客户要求重新交付
     */
    private void validateRetryDelivery(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        
        // 验证重新交付的原因
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("重新交付必须提供原因说明");
        }
        
        // 验证交付相关信息是否完整
        if (order.getRequirements() == null || order.getRequirements().trim().isEmpty()) {
            throw new IllegalArgumentException("交付要求不能为空");
        }
        
        if (order.getDdl() == null) {
            throw new IllegalArgumentException("交付截止日期不能为空");
        }
        
        if (order.getBusinessDeptId() == null) {
            throw new IllegalArgumentException("业务部门ID不能为空");
        }
        
        log.info("Retry delivery validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证异常处理完成
     * 
     * 适用场景：
     * 1. 异常已通过其他方式解决（如线下交付）
     * 2. 交付已通过其他渠道完成
     * 3. 客户确认可以直接进入确认阶段
     */
    private void validateExceptionResolved(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        
        // 验证解决方案说明
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("异常解决必须提供解决方案说明");
        }
        
        // 解决方案说明长度验证
        if (request.getReason().length() < 10) {
            throw new IllegalArgumentException("解决方案说明不能少于10个字符");
        }
        
        // 验证备注信息（用于记录解决过程）
        if (request.getRemark() == null || request.getRemark().trim().isEmpty()) {
            throw new IllegalArgumentException("异常解决必须提供备注信息");
        }
        
        // 验证交付相关信息完整性（确保可以进入待确认状态）
        if (order.getRequirements() == null || order.getRequirements().trim().isEmpty()) {
            throw new IllegalArgumentException("交付要求不能为空");
        }
        
        log.info("Exception resolution validation passed for order: {}", request.getDeliveryOrderNo());
    }
}
