package com.bxm.customer.service.strategy.impl;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.StatusValidationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 扣款异常状态验证策略
 * 
 * 处理从"扣款异常(待确认)"状态到其他状态的转换验证
 * 
 * 支持的状态转换：
 * 1. DEDUCTION_EXCEPTION -> DEDUCTION_CLOSED (关闭扣款)
 * 2. DEDUCTION_EXCEPTION -> CONFIRMED_PENDING_DEDUCTION (重新扣款)
 * 3. DEDUCTION_EXCEPTION -> DEDUCTION_COMPLETED (异常处理完成，直接完成扣款)
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class DeductionExceptionValidationStrategy implements StatusValidationStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            return false;
        }
        
        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED ||
               targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED;
    }

    @Override
    public void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        log.info("Validating status change from DEDUCTION_EXCEPTION to {} for order: {}", 
                request.getTargetStatus(), request.getDeliveryOrderNo());

        ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());
        
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED) {
            // 验证关闭扣款
            validateCloseDeduction(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            // 验证重新扣款
            validateRetryDeduction(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            // 验证异常处理完成
            validateExceptionResolved(order, request);
        } else {
            throw new IllegalArgumentException("不支持从扣款异常状态转换到: " + request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION;
    }

    /**
     * 验证关闭扣款
     * 
     * 适用场景：
     * 1. 客户拒绝扣款
     * 2. 扣款金额争议无法解决
     * 3. 业务取消，不再扣款
     */
    private void validateCloseDeduction(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限（需要财务主管或更高权限）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        
        // 验证关闭原因
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("关闭扣款必须提供详细原因");
        }
        
        // 关闭原因长度验证
        if (request.getReason().length() < 15) {
            throw new IllegalArgumentException("关闭扣款原因描述不能少于15个字符");
        }
        
        // 验证备注信息（用于记录关闭决策过程）
        if (request.getRemark() == null || request.getRemark().trim().isEmpty()) {
            throw new IllegalArgumentException("关闭扣款必须提供备注信息");
        }
        
        log.info("Close deduction validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证重新扣款
     * 
     * 适用场景：
     * 1. 异常问题已解决，可以重新尝试扣款
     * 2. 客户信息已更新，重新扣款
     */
    private void validateRetryDeduction(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        
        // 验证重新扣款的原因
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("重新扣款必须提供原因说明");
        }
        
        // 验证客户信息是否完整（重新扣款前的必要检查）
        if (order.getCustomerId() == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }
        
        if (order.getContactMobile() == null || order.getContactMobile().trim().isEmpty()) {
            throw new IllegalArgumentException("联络人手机号不能为空");
        }
        
        log.info("Retry deduction validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证异常处理完成
     * 
     * 适用场景：
     * 1. 异常已通过其他方式解决（如线下处理）
     * 2. 扣款已通过其他渠道完成
     */
    private void validateExceptionResolved(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        
        // 验证解决方案说明
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("异常解决必须提供解决方案说明");
        }
        
        // 解决方案说明长度验证
        if (request.getReason().length() < 10) {
            throw new IllegalArgumentException("解决方案说明不能少于10个字符");
        }
        
        // 验证备注信息（用于记录解决过程）
        if (request.getRemark() == null || request.getRemark().trim().isEmpty()) {
            throw new IllegalArgumentException("异常解决必须提供备注信息");
        }
        
        log.info("Exception resolution validation passed for order: {}", request.getDeliveryOrderNo());
    }
}
