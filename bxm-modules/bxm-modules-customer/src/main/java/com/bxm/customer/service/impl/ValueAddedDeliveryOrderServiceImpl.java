package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.mapper.ValueAddedDeliveryOrderMapper;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import com.bxm.customer.service.StateMachineManager;
import com.bxm.customer.service.ValueAddedValidationService;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 增值交付单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedDeliveryOrderServiceImpl extends ServiceImpl<ValueAddedDeliveryOrderMapper, ValueAddedDeliveryOrder> implements IValueAddedDeliveryOrderService
{

    @Autowired
    private ValueAddedValidationService validationService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @Autowired
    private StateMachineManager stateMachineManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueAddedDeliveryOrder upsert(ValueAddedDeliveryOrderVO orderVO) {
        try {
            log.info("Starting upsert operation for delivery order: {}", orderVO.getCustomerName());

            // 执行基础校验
            validateOrderVO(orderVO);

            // VO转换为DO
            ValueAddedDeliveryOrder order = new ValueAddedDeliveryOrder();
            BeanUtils.copyProperties(orderVO, order);

            // 根据增值事项名称执行特殊校验和处理逻辑
            validationService.validateByItemName(orderVO, order);

            // 查找现有记录
            ValueAddedDeliveryOrder existingOrder = findExistingOrder(order);

            if (existingOrder != null) {
                // 更新现有记录
                log.info("Updating existing delivery order: {}", existingOrder.getDeliveryOrderNo());
                updateExistingOrder(existingOrder, order);
                updateById(existingOrder);
                return existingOrder;
            } else {
                // 创建新记录
                // 设置默认状态
                if (StringUtils.isEmpty(order.getStatus())) {
                    order.setStatus(ValueAddedDeliveryOrderStatus.getDefaultStatus().getCode()); // 使用枚举设置默认状态
                }
                // 设置默认删除标志
                order.setIsDel(false);
                // 设置创建人信息
                try {
                    Long userId = SecurityUtils.getUserId();
                    order.setCreateUid(userId); // set creator uid
                    SysUser sysUser = remoteUserService.getByUserId(userId, SecurityConstants.INNER).getDataThrowException();
                    order.setCreateBy(Objects.isNull(sysUser) ? "" : sysUser.getNickName()); // set creator nick name to createBy
                } catch (Exception ex) {
                    log.warn("Failed to set create user info for delivery order, reason: {}", ex.getMessage());
                }
                save(order);
                log.info("Created new delivery order: {}", order.getDeliveryOrderNo());
                return order;
            }

        } catch (Exception e) {
            log.error("Failed to upsert delivery order for customer: {}", orderVO.getCustomerName(), e);
            throw new RuntimeException("保存增值交付单失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValueAddedDeliveryOrder getByDeliveryOrderNo(String deliveryOrderNo) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNo.trim())
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    @Override
    public ValueAddedDeliveryOrder getByCustomerIdAndItemType(Long customerId, Integer valueAddedItemType) {
        if (customerId == null || valueAddedItemType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getCustomerId, customerId)
                .eq(ValueAddedDeliveryOrder::getValueAddedItemTypeId, valueAddedItemType)
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    @Override
    public List<ValueAddedDeliveryOrder> query(DeliveryOrderQuery q) {
        // 构建动态查询条件
        LambdaQueryWrapper<ValueAddedDeliveryOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ValueAddedDeliveryOrder::getIsDel, false);

        // 基础文本/数字条件
        wrapper.like(StringUtils.isNotEmpty(q.getCustomerName()), ValueAddedDeliveryOrder::getCustomerName, q.getCustomerName())
               .eq(StringUtils.isNotEmpty(q.getDeliveryOrderNo()), ValueAddedDeliveryOrder::getDeliveryOrderNo, q.getDeliveryOrderNo())
               .eq(StringUtils.isNotEmpty(q.getCreditCode()), ValueAddedDeliveryOrder::getCreditCode, q.getCreditCode())
               .eq(StringUtils.isNotEmpty(q.getTaxNo()), ValueAddedDeliveryOrder::getTaxNo, q.getTaxNo())
               .eq(q.getTaxpayerType() != null, ValueAddedDeliveryOrder::getTaxpayerType, q.getTaxpayerType())
               .eq(q.getValueAddedItemTypeId() != null, ValueAddedDeliveryOrder::getValueAddedItemTypeId, q.getValueAddedItemTypeId())
               .eq(StringUtils.isNotEmpty(q.getStatus()), ValueAddedDeliveryOrder::getStatus, q.getStatus())
               .eq(q.getInitiateDeptId() != null, ValueAddedDeliveryOrder::getInitiateDeptId, q.getInitiateDeptId())
               .eq(q.getBusinessDeptId() != null, ValueAddedDeliveryOrder::getBusinessDeptId, q.getBusinessDeptId())
               .eq(q.getBusinessTopDeptId() != null, ValueAddedDeliveryOrder::getBusinessTopDeptId, q.getBusinessTopDeptId())
               .eq(q.getCreateUid() != null, ValueAddedDeliveryOrder::getCreateUid, q.getCreateUid());

        // 账期范围查询：查询条件的账期范围与数据库记录的账期范围有交集
        if (q.getAccountingPeriodStart() != null && q.getAccountingPeriodEnd() != null) {
            // 查询账期范围与记录账期范围有交集的记录
            // 条件：查询开始 <= 记录结束 AND 查询结束 >= 记录开始
            Integer queryStart = q.getAccountingPeriodStart();
            Integer queryEnd = q.getAccountingPeriodEnd();
            if (queryStart > queryEnd) {
                // 容错：如果开始>结束，自动交换
                Integer temp = queryStart;
                queryStart = queryEnd;
                queryEnd = temp;
            }
            wrapper.le(ValueAddedDeliveryOrder::getAccountingPeriodStart, queryEnd)
                   .ge(ValueAddedDeliveryOrder::getAccountingPeriodEnd, queryStart);
        } else if (q.getAccountingPeriodStart() != null) {
            // 只有开始时间：记录的结束时间 >= 查询开始时间
            wrapper.ge(ValueAddedDeliveryOrder::getAccountingPeriodEnd, q.getAccountingPeriodStart());
        } else if (q.getAccountingPeriodEnd() != null) {
            // 只有结束时间：记录的开始时间 <= 查询结束时间
            wrapper.le(ValueAddedDeliveryOrder::getAccountingPeriodStart, q.getAccountingPeriodEnd());
        }

        // DDL 日期范围
        LocalDate ddlStart = q.getDdlStart();
        LocalDate ddlEnd = q.getDdlEnd();
        if (ddlStart != null && ddlEnd != null) {
            if (!ddlEnd.isBefore(ddlStart)) {
                wrapper.between(ValueAddedDeliveryOrder::getDdl, ddlStart, ddlEnd);
            } else {
                // 容错：若结束早于开始，自动纠正
                wrapper.between(ValueAddedDeliveryOrder::getDdl, ddlEnd, ddlStart);
            }
        } else if (ddlStart != null) {
            wrapper.ge(ValueAddedDeliveryOrder::getDdl, ddlStart);
        } else if (ddlEnd != null) {
            wrapper.le(ValueAddedDeliveryOrder::getDdl, ddlEnd);
        }

        // 排序：默认创建时间倒序
        wrapper.orderByDesc(ValueAddedDeliveryOrder::getCreateTime);

        // BaseController.startPage() 注入
        return this.list(wrapper);
    }

    @Override
    public List<ValueAddedDeliveryOrderVO> queryVO(DeliveryOrderQuery q) {
        // 调用现有的query方法获取实体列表
        List<ValueAddedDeliveryOrder> deliveryOrders = query(q);

        if (deliveryOrders.isEmpty()) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的itemTypeId
        Set<Integer> itemTypeIds = deliveryOrders.stream()
                .map(ValueAddedDeliveryOrder::getValueAddedItemTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询增值事项类型信息，建立itemTypeId到itemName的映射
        Map<Integer, String> itemTypeIdToNameMap = new HashMap<>();
        if (!itemTypeIds.isEmpty()) {
            try {
                // 根据itemTypeId查询对应的增值事项类型
                List<Long> itemTypeIdList = itemTypeIds.stream()
                        .map(Long::valueOf)
                        .collect(Collectors.toList());

                LambdaQueryWrapper<ValueAddedItemType> itemTypeWrapper = new LambdaQueryWrapper<>();
                itemTypeWrapper.in(ValueAddedItemType::getId, itemTypeIdList)
                        .ne(ValueAddedItemType::getIsDel, true);

                List<ValueAddedItemType> itemTypes = valueAddedItemTypeService.list(itemTypeWrapper);
                if (itemTypes != null && !itemTypes.isEmpty()) {
                    itemTypeIdToNameMap = itemTypes.stream()
                            .filter(item -> item.getId() != null && item.getItemName() != null)
                            .collect(Collectors.toMap(
                                    item -> item.getId().intValue(),
                                    ValueAddedItemType::getItemName,
                                    (existing, replacement) -> existing
                            ));
                }

                log.debug("Successfully loaded {} item type mappings", itemTypeIdToNameMap.size());
            } catch (Exception e) {
                log.error("Failed to load item type mappings", e);
                // 继续执行，但itemName将为空
            }
        }

        // 转换实体对象到VO对象
        final Map<Integer, String> finalItemTypeIdToNameMap = itemTypeIdToNameMap;
        return deliveryOrders.stream()
                .map(order -> convertToVO(order, finalItemTypeIdToNameMap))
                .collect(Collectors.toList());
    }

    /**
     * 将实体对象转换为VO对象
     *
     * @param order 增值交付单实体对象
     * @param itemTypeIdToNameMap itemTypeId到itemName的映射
     * @return VO对象
     */
    private ValueAddedDeliveryOrderVO convertToVO(ValueAddedDeliveryOrder order, Map<Integer, String> itemTypeIdToNameMap) {
        ValueAddedDeliveryOrderVO vo = new ValueAddedDeliveryOrderVO();

        // 使用BeanUtils复制基础字段
        BeanUtils.copyProperties(order, vo);

        // 映射itemId到itemName
        if (order.getValueAddedItemTypeId() != null) {
            String itemName = itemTypeIdToNameMap.get(order.getValueAddedItemTypeId());
            vo.setItemName(itemName != null ? itemName : "");
            log.debug("Mapped itemTypeId {} to itemName: {}", order.getValueAddedItemTypeId(), itemName);
        }

        return vo;
    }

    /**
     * 验证订单VO
     */
    private void validateOrderVO(ValueAddedDeliveryOrderVO orderVO) {
        // 验证交付单编号（必填字段）
        if (StringUtils.isEmpty(orderVO.getDeliveryOrderNo())) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 验证交付单编号格式（VAD开头，总长度19位）
        // 格式：VAD + yyMMddHHmmsss(13位数字) + 3位随机码(数字+大写字母)
        if (!orderVO.getDeliveryOrderNo().matches("^VAD\\d{13}[0-9A-Z]{3}$")) {
            throw new IllegalArgumentException("交付单编号格式不正确，应为VAD+时间戳到毫秒+3位随机码，总长度19位");
        }

        // 验证状态（如果提供了状态）
        if (StringUtils.isNotEmpty(orderVO.getStatus()) && !ValueAddedDeliveryOrderStatus.isValid(orderVO.getStatus())) {
            throw new IllegalArgumentException("无效的交付状态: " + orderVO.getStatus());
        }

        // 验证纳税性质
        if (orderVO.getTaxpayerType() != null && (orderVO.getTaxpayerType() < 1 || orderVO.getTaxpayerType() > 2)) {
            throw new IllegalArgumentException("无效的纳税性质: " + orderVO.getTaxpayerType());
        }

        // 验证账期逻辑（账期格式为YYYYMM的整数，如：202301）
        if (orderVO.getAccountingPeriodStart() != null && orderVO.getAccountingPeriodEnd() != null) {
            if (orderVO.getAccountingPeriodStart() > orderVO.getAccountingPeriodEnd()) {
                throw new IllegalArgumentException("账期开始时间不能晚于结束时间");
            }
        }
    }

    /**
     * 查找现有记录
     */
    private ValueAddedDeliveryOrder findExistingOrder(ValueAddedDeliveryOrder order) {
        if (StringUtils.isNotEmpty(order.getDeliveryOrderNo())) {
            return getByDeliveryOrderNo(order.getDeliveryOrderNo());
        }
        return null;
    }

    /**
     * 更新现有记录
     */
    private void updateExistingOrder(ValueAddedDeliveryOrder existingOrder, ValueAddedDeliveryOrder newOrder) {
        // 保留原有的ID、交付单编号和创建相关信息
        Long originalId = existingOrder.getId();
        String originalDeliveryOrderNo = existingOrder.getDeliveryOrderNo();
        String originalCreateBy = existingOrder.getCreateBy();
        Long originalCreateUid = existingOrder.getCreateUid();
        // 使用BeanUtils复制所有属性
        BeanUtils.copyProperties(newOrder, existingOrder);
        // 恢复不应该被更新的字段
        existingOrder.setId(originalId);
        existingOrder.setDeliveryOrderNo(originalDeliveryOrderNo);
        existingOrder.setCreateBy(originalCreateBy);
        existingOrder.setCreateUid(originalCreateUid);
    }

    @Override
    public void changeStatus(StatusChangeRequestDTO request) {
        try {
            log.info("Starting status change for order: {} to status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());

            // 参数验证
            if (request == null) {
                throw new IllegalArgumentException("状态变更请求不能为空");
            }
            if (StringUtils.isEmpty(request.getDeliveryOrderNo())) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }
            if (StringUtils.isEmpty(request.getTargetStatus())) {
                throw new IllegalArgumentException("目标状态不能为空");
            }

            // 1. 获取当前交付单信息
            ValueAddedDeliveryOrder order = getByDeliveryOrderNo(request.getDeliveryOrderNo());
            if (order == null) {
                throw new IllegalArgumentException("交付单不存在: " + request.getDeliveryOrderNo());
            }

            // 2. 解析当前状态和目标状态
            ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());

            if (currentStatus == null) {
                throw new IllegalArgumentException("当前状态无效: " + order.getStatus());
            }
            if (targetStatus == null) {
                throw new IllegalArgumentException("目标状态无效: " + request.getTargetStatus());
            }

            // 3. 检查状态是否相同
            if (currentStatus == targetStatus) {
                throw new IllegalArgumentException("当前状态与目标状态相同，无需变更");
            }

            // 4. 委托给状态机管理器处理验证
            stateMachineManager.validateAndChangeStatus(order, currentStatus, targetStatus, request);

            // 5. 执行数据库更新
            boolean updateResult = updateById(order);
            if (!updateResult) {
                throw new RuntimeException("状态更新失败");
            }

            log.info("Status change completed successfully for order: {} from {} to {}",
                    request.getDeliveryOrderNo(), currentStatus.getDescription(), targetStatus.getDescription());

        } catch (Exception e) {
            log.error("Status change failed for order: {}, error: {}",
                    request != null ? request.getDeliveryOrderNo() : "unknown", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAvailableStatuses(String deliveryOrderNo) {
        try {
            log.info("Getting available statuses for order: {}", deliveryOrderNo);

            // 参数验证
            if (StringUtils.isEmpty(deliveryOrderNo)) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }

            // 获取当前交付单信息
            ValueAddedDeliveryOrder order = getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                log.warn("Delivery order not found: {}", deliveryOrderNo);
                return new ArrayList<>();
            }

            // 解析当前状态
            ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            if (currentStatus == null) {
                log.warn("Invalid current status for order: {}, status: {}", deliveryOrderNo, order.getStatus());
                return new ArrayList<>();
            }

            // 获取可用的下一状态
            List<ValueAddedDeliveryOrderStatus> availableStatuses =
                    stateMachineManager.getAvailableNextStatuses(currentStatus);

            log.info("Found {} available statuses for order: {}", availableStatuses.size(), deliveryOrderNo);
            return availableStatuses;

        } catch (Exception e) {
            log.error("Failed to get available statuses for order: {}, error: {}",
                    deliveryOrderNo, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

}
