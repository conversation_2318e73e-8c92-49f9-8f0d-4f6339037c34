package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 增值期间月度表对象 c_value_added_period_month
 *
 *
 * @date 2025-01-13
 */
@Data
@ApiModel("增值期间月度表对象")
@Accessors(chain = true)
@TableName("c_value_added_period_month")
public class ValueAddedPeriodMonth extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 业务部门ID */
    @Excel(name = "业务部门ID")
    @TableField("business_dept_id")
    @ApiModelProperty(value = "业务部门ID")
    private Long businessDeptId;

    /** 顶级业务部门ID，集团级部门ID */
    @Excel(name = "顶级业务部门ID，集团级部门ID")
    @TableField("business_top_dept_id")
    @ApiModelProperty(value = "顶级业务部门ID，集团级部门ID")
    private Long businessTopDeptId;

    /** 顾问部门ID */
    @Excel(name = "顾问部门ID")
    @TableField("advisor_dept_id")
    @ApiModelProperty(value = "顾问部门ID")
    private Long advisorDeptId;

    /** 顾问顶级部门ID */
    @Excel(name = "顾问顶级部门ID")
    @TableField("advisor_top_dept_id")
    @ApiModelProperty(value = "顾问顶级部门ID")
    private Long advisorTopDeptId;

    /** 会计部门ID */
    @Excel(name = "会计部门ID")
    @TableField("accounting_dept_id")
    @ApiModelProperty(value = "会计部门ID")
    private Long accountingDeptId;

    /** 会计顶级部门ID */
    @Excel(name = "会计顶级部门ID")
    @TableField("accounting_top_dept_id")
    @ApiModelProperty(value = "会计顶级部门ID")
    private Long accountingTopDeptId;

    /** 服务状态，1-服务中，2-已暂停，3-冻结中 */
    @Excel(name = "服务状态", readConverterExp = "1=服务中,2=已暂停,3=冻结中")
    @TableField("service_status")
    @ApiModelProperty(value = "服务状态，1-服务中，2-已暂停，3-冻结中")
    private Integer serviceStatus;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 税务人定性，1-小规模，2-一般纳税人 */
    @Excel(name = "税务人定性", readConverterExp = "1=小规模,2=一般纳税人")
    @TableField("tax_type")
    @ApiModelProperty(value = "税务人定性，1-小规模，2-一般纳税人")
    private Integer taxType;
}
