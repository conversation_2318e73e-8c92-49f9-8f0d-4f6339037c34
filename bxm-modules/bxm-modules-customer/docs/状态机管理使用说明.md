# 增值交付单状态机管理使用说明

## 概述

本文档介绍增值交付单状态机管理功能的使用方法。该功能基于策略模式设计，提供了优雅可扩展的状态转换管理。

## 状态定义

增值交付单包含以下状态：

### 正常流程状态
1. **DRAFT** - 草稿状态（初始状态）
2. **SAVED_PENDING_SUBMIT** - 已保存待提交
3. **SUBMITTED_PENDING_DELIVERY** - 已提交待交付
4. **PENDING_CONFIRMATION** - 已交付待确认
5. **CONFIRMED_PENDING_DEDUCTION** - 已确认待扣款
6. **DEDUCTION_COMPLETED** - 已扣款（正常完成状态）

### 异常流程状态
7. **DELIVERY_EXCEPTION** - 交付异常(待确认)
8. **DELIVERY_CLOSED** - 已关闭交付
9. **DEDUCTION_EXCEPTION** - 扣款异常(待确认)
10. **DEDUCTION_CLOSED** - 已关闭扣款

## 状态转换规则

### 正常流程
```
DRAFT → SAVED_PENDING_SUBMIT → SUBMITTED_PENDING_DELIVERY → PENDING_CONFIRMATION → CONFIRMED_PENDING_DEDUCTION → DEDUCTION_COMPLETED
```

### 异常处理流程
- **交付异常**: SUBMITTED_PENDING_DELIVERY → DELIVERY_EXCEPTION → DELIVERY_CLOSED
- **扣款异常**: CONFIRMED_PENDING_DEDUCTION → DEDUCTION_EXCEPTION → DEDUCTION_CLOSED

### 退回流程
- 可以从任何状态退回到前一个状态（需要提供原因）

## API接口

### 1. 修改状态接口

**接口地址**: `POST /valuedAddedDeliveryOrder/changeStatus`

**请求参数**:
```json
{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "SAVED_PENDING_SUBMIT",
  "reason": "客户确认提交申请",
  "operatorId": 1,
  "operatorName": "张三",
  "remark": "正常提交流程"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "状态修改成功",
  "data": "状态修改成功"
}
```

### 2. 获取可用状态接口

**接口地址**: `GET /valuedAddedDeliveryOrder/availableStatuses/{deliveryOrderNo}`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "code": "SAVED_PENDING_SUBMIT",
      "description": "已保存待提交"
    }
  ]
}
```

## 验证规则

每个状态转换都有对应的验证规则：

### 待提交状态验证
- 客户企业名称不能为空
- 统一社会信用代码不能为空
- 增值事项类型不能为空
- 交付截止日期不能为空

### 交付状态验证
- 交付要求不能为空
- 业务部门ID不能为空
- 异常转换需要详细原因（≥10字符）

### 确认状态验证
- 账务类型信息不能为空
- 账期信息必须完整且逻辑正确
- 联络人手机号不能为空

### 扣款状态验证
- 客户信息完整性验证
- 纳税人类型必须有效
- 异常处理需要详细说明（≥10字符）

## 扩展指南

### 添加新状态
1. 在 `ValueAddedDeliveryOrderStatus` 枚举中添加新状态
2. 创建对应的验证策略类实现 `StatusValidationStrategy` 接口
3. 在 `StatusTransitionConfig` 中配置状态转换规则

### 添加新验证规则
1. 在对应的策略类中添加验证逻辑
2. 确保抛出 `IllegalArgumentException` 并提供清晰的错误信息

## 错误处理

### 常见错误
- **参数验证失败**: 返回具体的验证错误信息
- **状态转换不被允许**: 返回不支持的转换提示
- **交付单不存在**: 返回交付单不存在错误
- **系统异常**: 返回通用错误信息

### 日志记录
- 所有状态转换都会记录详细日志
- 验证失败会记录具体原因
- 支持不同级别的日志输出

## 配置说明

可以通过 `application.yml` 配置状态转换规则：

```yaml
bxm:
  delivery-order:
    status-transition:
      strict-mode: false
      log-level: INFO
      audit-enabled: true
      timeout-seconds: 30
```

## 注意事项

1. 状态转换是不可逆的，请谨慎操作
2. 异常状态需要人工干预处理
3. 终态状态（已扣款、已关闭）一般不允许转换
4. 所有状态转换都会进行权限验证
5. 建议在生产环境中启用审计功能
